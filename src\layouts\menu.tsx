import Logo from '@/assets/logo.svg';
import {
  AIArrowBottomSvg,
  AiChat,
  AiDelete,
  AiDrawerIcon,
  AiEdit,
  AiEditII,
  AiKnowledge,
  AiMore,
  AiOffice,
  AiRecent,
  AiSmartEngineering,
  AiSmartManagement,
  AiToolbox,
  MenuJia,
  OpenSidebarIcon,
} from '@/assets/svg';
import PWAInstallButton from '@/components/PWAInstaller';
import { ALWAYS_SHOW_ONLINE_QUERY_STATUS_APPS } from '@/config';
import { ConfigWith4A } from '@/config/4AConfig';
import { AuthModelState } from '@/models/auth';
import { HistoryModelState } from '@/models/historyChat';
import MeetingTitle from '@/pages/Office/MeetingSummary/components/MeetingTitle';
import { deleteChatById, updateChatTitle } from '@/services/chat';
import type { ChatItem } from '@/services/types';
import { dispatchInUtils, getState, setAccessToken } from '@/utils';
import { IconAlertCircle, IconCopy, IconTick } from '@douyinfe/semi-icons';
import {
  Avatar,
  Button,
  Icon,
  Input,
  Modal,
  Popover,
  Toast,
  Tooltip,
  Typography,
} from '@douyinfe/semi-ui';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import { useLocation, useParams } from 'react-router-dom';
import { Link, Outlet, useDispatch, useNavigate, useSelector } from 'umi';
import styles from './index.less';

const FixedMenu = [
  {
    name: '知识库',
    icon: <AiKnowledge />,
    type: '',
    path: '/chat/knowledge',
  },
  {
    name: '高效办公',
    icon: <AiOffice />,
    type: '',
    path: '/chat/office',
  },
  {
    name: '智慧工程',
    icon: <AiSmartEngineering />,
    type: '',
    path: '/chat/smart-engineering',
  },
  {
    name: '智慧管理',
    icon: <AiSmartManagement />,
    type: '',
    path: '/chat/smart-management',
  },
  {
    name: 'AI工具箱',
    icon: <AiToolbox />,
    type: '',
    path: '/chat/ai-toolbox',
  },
  // {
  //   name: '智能PPT',
  //   icon: <Ppt />,
  //   type: '',
  //   path: '/chat/ppt',
  // },
];

export default function Menu() {
  let navigate = useNavigate();
  const { Paragraph, Text, Title } = Typography;

  const { user } = useSelector((state: { auth: AuthModelState }) => state.auth);

  const [visible, setVisible] = useState(false);

  const [menuVisible, setMenuVisible] = useState(true);
  /** 弹窗标题 */
  const [modalTitle, setModalTitle] = useState('');
  // 历史
  const [historyVisible, setHistoryVisible] = useState(false);

  // 是否展示AI工具箱
  const [aiToolboxVisible, setAiToolboxVisible] = useState(false);
  const location = useLocation();

  const { pathname } = useLocation();

  const { chatId } = useParams<{ chatId: string }>();

  const [meetingStateInfo, setMeetingStateInfo] = useState<{
    id: string;
    fileName: string;
  }>({ id: '', fileName: '' });

  const [standbyChatId, setStandbyChatId] = useState('');
  const { historyList, chatTitle } = useSelector(
    (state: { historyChat: HistoryModelState }) => state.historyChat,
  );

  const pageMode: string = useSelector(
    (state: { pageLayout: { mode: '' } }) => state.pageLayout.mode,
  );

  const chatContainerHide: string = useSelector(
    (state: { pageLayout: { chatContainerHide: '' } }) => state.pageLayout.chatContainerHide,
  );

  useEffect(() => {
    const pathname = location.pathname;
    if (
      ['doc', 'write', 'meeting', 'report_gen', 'ent_credit'].indexOf(pageMode) > -1 ||
      pathname === '/chat/write'
    )
      setMenuVisible(false);
    else setMenuVisible(true);
  }, [pageMode]);

  useEffect(() => {
    dispatchInUtils({
      type: 'historyChat/fetchHistoryList',
      payload: {
        chatId,
      },
    });
  }, []);

  useEffect(() => {
    dispatchInUtils({
      type: 'historyChat/updateTilte',
      payload: {
        chatId,
      },
    });
    if (pathname.includes('/meeting')) {
      setMeetingStateInfo(location.state as any);
    }
  }, [pathname]);

  /**
   * 弹窗取消
   */
  const handleCancel = () => {
    setVisible(false);
    setModalTitle('');
    setStandbyChatId('');
  };
  /**
   * 打开弹窗
   */
  const showDialog = () => {
    setModalTitle(chatTitle);
    setVisible(true);
  };

  // 历史重命名
  const historyReName = (Item: ChatItem) => {
    setStandbyChatId(Item.chatId);
    setModalTitle(Item.chatTitle);
    setVisible(true);
  };

  // 更新标题
  const updateTitle = async () => {
    try {
      if (!modalTitle) {
        Toast.error('请输入对话名称');
        return;
      }
      const res = await updateChatTitle({
        chatId: standbyChatId || chatId || '',
        title: modalTitle,
      });
      setVisible(false);
      if (res.data) {
        dispatchInUtils({
          type: 'historyChat/fetchHistoryList',
          payload: {
            chatId,
          },
        });
      }
    } catch (error: any) {
      Toast.error(error.message);
    } finally {
      setStandbyChatId('');
    }
  };

  // 获取输入框字符长度
  const getValueLength = (str: string) => {
    const string = str.split('');
    return string?.length || 0;
  };

  // 删除历史
  const delChat = async (Item: any) => {
    try {
      const res = await deleteChatById(Item.chatId);
      const data = res.data;
      if (data) {
        dispatchInUtils({
          type: 'historyChat/fetchHistoryList',
          payload: {
            chatId,
          },
        });
        if (Item.chatId === chatId) {
          navigate('/chat', { replace: true });
        }
      }
    } catch (error: any) {
      Toast.error(error.message);
    }
  };

  const logout = () => {
    Modal.warning({
      title: <div style={{ fontSize: '16px' }}>确定退出登录吗？</div>,
      onOk: () => {
        setAccessToken('');
        if (process.env.NODE_ENV === 'development') {
          navigate('/login');
          return;
        }
        window.location.href = ConfigWith4A.LogoutUrl();
      },
    });
  };

  // 历史删除
  const historyDelete = (Item: any) => {
    Modal.error({
      title: <div style={{ fontSize: '16px' }}>确定删除对话</div>,
      icon: <IconAlertCircle style={{ color: 'rgba(250, 140, 22, 1)' }} />,
      okText: '删除',
      content: (
        <div style={{ fontSize: '14px', color: '#555', marginLeft: '-36px' }}>
          删除后，聊天记录不可恢复。
        </div>
      ),
      onOk: () => {
        delChat(Item);
      },
      onCancel: () => {
        setStandbyChatId('');
      },
    });
  };

  // 用户个人资料
  const userProfile = () => {
    return (
      <div>
        <div className={styles.chattopbar_userinfo_profile}>
          <div className={styles.chattopbar_userinfo_profile_item}>
            姓名：{user?.name || user?.username}
          </div>
          <div className={styles.chattopbar_userinfo_profile_item}>
            手机号：
            {user?.mobile ? (
              <Paragraph
                className="flex items-center"
                copyable={{
                  content: user?.mobile,
                  render: (copied, doCopy) => {
                    return copied ? (
                      <IconTick style={{ color: 'rgba(0, 91, 248, 1)' }} />
                    ) : (
                      <Tooltip content={'复制'} position="top">
                        <IconCopy onClick={doCopy} />
                      </Tooltip>
                    );
                  },
                }}
              >
                {user?.mobile}
              </Paragraph>
            ) : (
              ''
            )}
          </div>
          {user?.email ? (
            <div className={styles.chattopbar_userinfo_profile_item}>
              邮箱：
              <Paragraph
                className="flex items-center"
                copyable={{
                  content: user?.email,
                  render: (copied, doCopy) => {
                    return copied ? (
                      <IconTick style={{ color: 'rgba(0, 91, 248, 1)' }} />
                    ) : (
                      <Tooltip content={'复制'} position="top">
                        <IconCopy onClick={doCopy} />
                      </Tooltip>
                    );
                  },
                }}
              >
                <span className={styles.userEmail}>{user?.email}</span>
              </Paragraph>
            </div>
          ) : (
            ''
          )}
        </div>
        <div className="py-[4px] w-full">
          <div className="h-[1px] bg-gray-50"></div>
        </div>
        <Button
          theme="outline"
          type="tertiary"
          size="large"
          onClick={logout}
          style={{ height: '38px' }}
          className={styles.chattopbar_logout}
        >
          退出登录
        </Button>
      </div>
    );
  };

  // 历史对话操作
  const HistoryAction = (Item: any) => {
    return (
      <div className={styles.history_action}>
        <div className={styles.history_action_item} onClick={() => historyReName(Item)}>
          <div className={styles.history_action_icon}>
            <AiEditII />
          </div>
          重命名
        </div>
        <div
          className={styles.history_action_item}
          style={{ color: 'rgba(255, 59, 49, 1)' }}
          onClick={() => historyDelete(Item)}
        >
          <div className={styles.history_action_icon}>
            <AiDelete />
          </div>
          <span>删除</span>
        </div>
      </div>
    );
  };

  const meetingHeaderClick = () => {
    dispatchInUtils({
      type: 'pageLayout/changePageMode',
      payload: '',
    });
    navigate(-1);
  };

  // 取消除了当前chatId的其他页面流式请求
  const abortOtherChatIdStream = (currChatId: string, appCode: string) => {
    const chatMsg = getState().chat.chatMsg || {};
    Object.keys(chatMsg).forEach((_chatId) => {
      if (_chatId !== 'appCode' && _chatId !== currChatId && chatMsg![_chatId].pending) {
        chatMsg![_chatId]?.abortController?.abort();
        dispatchInUtils({
          type: 'chat/saveMessage',
          payload: {
            appCode,
            [_chatId]: {
              ...chatMsg![_chatId],
              pending: false,
            },
          },
        });
        dispatchInUtils({
          type: 'chat/updateAbortController',
          payload: {
            appCode,
            chatId: _chatId,
            abortController: null,
          },
        });
      }
    });
  };

  const nativeTo = (chatId: string, appCode: string, routeId?: number, item: any) => {
    const stateData = {
      routeId,
      prompt: item.prompt || '',
    };

    let chatPath = 'chat';
    // if (appCode == 'write') chatPath = 'chat-read';
    if (item.routeId) {
      // 遍历 chatMsg
      if (appCode === 'image') abortOtherChatIdStream(chatId, appCode);
      dispatch({
        type: 'chat/setImageRouteId',
        payload: `${item.routeId}`,
      });
      navigate(`/${chatPath}/${chatId}?appCode=${appCode}&code=${item.routeId}`, {
        state: stateData,
      });
    } else {
      navigate(`/${chatPath}/${chatId}?appCode=${appCode}`, { state: stateData });
    }
  };

  const dispatch = useDispatch();
  const onclick = async (item: any) => {
    // 切换菜单的时候，清除pageMode
    dispatchInUtils({
      type: 'pageLayout/changePageMode',
      payload: '',
    });
    dispatchInUtils({
      type: 'aiWrite/clearContent',
    });
    if (item.name === 'AI工具箱') {
      setAiToolboxVisible(true);
    } else {
      setAiToolboxVisible(false);
    }
  };
  const handClick = (item: any) => {
    if (ALWAYS_SHOW_ONLINE_QUERY_STATUS_APPS.includes(item.appCode)) {
      dispatch({
        type: 'chat/setOnlineStatus',
        payload: true,
      });
    } else {
      dispatch({
        type: 'chat/setOnlineStatus',
        payload: false,
      });
    }
    dispatch({
      type: 'chat/setIsShowVditor',
      payload: false,
    });
    dispatch({
      type: 'chat/setIsExpand',
      payload: false,
    });
    dispatchInUtils({
      type: 'chat/updateRichTextAreaContent',
      payload: '',
    });
    dispatchInUtils({
      type: 'aiWrite/clearContent',
    });
    // 富文本状态清除
    dispatchInUtils({
      type: 'chat/setRichTextContent',
      payload: {
        useRichText: false,
        richTextValue: [
          {
            type: 'paragraph',
            children: [{ text: '' }],
          },
        ],
      },
    });
    const boundFunction = nativeTo.bind(null, item.chatId, item.appCode, item?.routeId, item);
    boundFunction();
  };
  useEffect(() => {
    // 判断当前路径是否包含 'ai-toolbox'
    const shouldShowToolbox = location.pathname.includes('ai-toolbox');
    setAiToolboxVisible(shouldShowToolbox);
  }, [location.pathname]);

  const handleMeetingSummary = () => {
    dispatchInUtils({
      type: 'pageLayout/changePageMode',
      payload: 'meeting',
    });
  };

  const isCurrentPath = (path: string) => {
    if (['/chat/office/meeting'].includes(pathname)) {
      return pathname.includes(path);
    }
    return pathname === path;
  };

  // useEffect(() => {
  //   if (
  //     chatId &&
  //     historyList.length > 0 &&
  //     !historyList.find((item: any) => item.chatId === chatId)
  //   ) {
  //     navigate('/chat');
  //   }
  // }, [chatId, historyList]);

  return (
    <>
      {!menuVisible && !chatContainerHide && (
        <div className={styles.sidebar_open} onClick={() => setMenuVisible(true)}>
          <div className="flex items-center gap-2">
            <Tooltip content={'展开侧栏'} position="right">
              <Button theme="borderless" icon={<OpenSidebarIcon />}></Button>
            </Tooltip>
            {pageMode === 'doc' && (
              <Button className={styles.sidebar_newButton} onClick={() => navigate('/chat')}>
                <MenuJia />
                <span>新对话</span>
              </Button>
            )}
          </div>
        </div>
      )}
      <div className={classNames(styles.sidebar, { '!w-0': !menuVisible })}>
        <div className="w-[240px] h-full overflow-hidden flex flex-col">
          <div
            className={classNames([
              styles.sidebarTop,
              'flex items-center justify-between mt-12px ml-16px mr-10px',
            ])}
          >
            <div
              className="flex items-center"
              style={{ cursor: 'pointer' }}
              onClick={() => {
                if (location.pathname === '/chat') {
                  return;
                }
                navigate('/chat');
              }}
            >
              <span className="text-semi-color-primary">
                <img src={Logo} className="w-[24px] h-[24px]" />
              </span>
              <span className={styles.title}>瞳界AI应用平台</span>
            </div>
            <div onClick={() => setMenuVisible(false)}>
              <Tooltip content={'收起侧栏'} position="right">
                <Button theme="borderless" icon={<AiDrawerIcon />}></Button>
              </Tooltip>
            </div>
          </div>
          <Link to="/chat" className={`${styles.sidebar_newchatbtn}  flex`}>
            <AiChat />
            <span className="ml-[8px]">新对话</span>
          </Link>
          <div className={styles.sidebar_menu}>
            {FixedMenu.map((item, index) => {
              return (
                <div
                  key={index}
                  className={`${styles.sidebar_menu_item} ${
                    isCurrentPath(item.path) ? `${styles.linkActive}` : ''
                  }`}
                  onClick={() => onclick(item)}
                >
                  <Link to={item.path} className="flex flex-1 p-[8px] items-center w-full">
                    {/* <img src={item.icon} /> */}
                    <Icon svg={item.icon} className={styles['semi-icon']} />
                    <span>{item.name}</span>
                  </Link>
                </div>
              );
            })}
          </div>
          <div className={styles.sidebar_recent}>
            <div
              className={styles.sidebar_recent_header}
              onClick={() => setHistoryVisible(!historyVisible)}
            >
              <div className="flex items-center">
                <AiRecent />
                <span className="ml-[8px]">最近对话</span>
              </div>
              <div
                className={`transition-transform duration-500 ${
                  historyVisible ? 'rotate-180' : ''
                }`}
              >
                <AIArrowBottomSvg />
              </div>
            </div>
            <div className={styles.sidebar_recent_history}>
              <div className={`${historyVisible ? 'h-0 !flex-0 !overflow-hidden' : ''}`}>
                {historyList?.length
                  ? historyList.map((item: ChatItem) => {
                      return (
                        <div
                          className={classNames(styles.history_item, {
                            [styles.history_item_active]: chatId === item.chatId,
                          })}
                          key={item.chatId}
                          onClick={() => {
                            handClick(item);
                          }}
                        >
                          <div title={item.chatTitle} className={styles.history_item_text}>
                            {item.chatTitle}
                          </div>
                          <div
                            className={styles.history_item_action}
                            onClick={(event) => event.stopPropagation()}
                          >
                            <Popover
                              content={HistoryAction(item)}
                              position="bottomLeft"
                              trigger="hover"
                              zIndex={100}
                              className={styles.history_action_popover}
                            >
                              <div>
                                <AiMore />
                              </div>
                            </Popover>
                          </div>
                        </div>
                      );
                    })
                  : ''}
                {/* <div className={styles.history_item}>
                  <Link to="/chat/123" className={styles.history_item_text}>
                    最近对话最近对话
                  </Link>
                  <div className={styles.history_item_action}>
                    <Popover
                      content={HistoryAction({})}
                      position="bottomLeft"
                      trigger="click"
                      className={styles.history_action_popover}
                    >
                      <div>
                        <MoreSvg />
                      </div>
                    </Popover>
                  </div>
                </div>
                <div className={styles.history_item}>
                  <Link to="/chat/234" className={styles.history_item_text}>
                    最近对话最近对话
                  </Link>
                  <div className={styles.history_item_action}>
                    <MoreSvg />
                  </div>
                </div> */}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className={styles.chat}>
        {['doc', 'ppt', 'write', 'meeting', 'report_gen', 'ent_credit'].indexOf(pageMode) > -1 ? (
          ''
        ) : (
          <div className={styles.chattopbar} style={!menuVisible ? { marginLeft: 32 } : {}}>
            <div
              className={classNames(styles.chattopbar_title, {
                [styles.chattopbar_title__hover]: pathname !== '/chat/office',
              })}
              onClick={!aiToolboxVisible && !pathname.includes('/meeting') ? showDialog : undefined}
            >
              <span className={styles.chattopbar_title_text}>
                {aiToolboxVisible ? 'AI工具箱' : null}
                {pathname.includes('/meeting') ? (
                  <MeetingTitle
                    className={styles.meetingTitleOfMenu}
                    title={meetingStateInfo?.fileName}
                    onBackClick={meetingHeaderClick}
                  />
                ) : (
                  chatTitle
                )}
              </span>
              {!aiToolboxVisible && !pathname.includes('/meeting') && (
                <AiEdit className={styles.editIcon} />
              )}
            </div>
            <div className={styles.chattopbar_right}>
              {/* <GlobalSearch contentArea={`.${styles.chatMain}`} /> */}
              {pathname.includes('/meeting') && (
                <Tooltip content={'显示总结内容'}>
                  <Button
                    theme="borderless"
                    icon={<OpenSidebarIcon />}
                    onClick={handleMeetingSummary}
                  ></Button>
                </Tooltip>
              )}

              {/* {findInputVisible ? (
                <InputGroup>
                  <Input
                    value={findContent}
                    placeholder="输入内容回车进行搜索"
                    className={styles.chattopbar_right_input}
                    prefix={<SearchIcon />}
                    suffix={
                      <Button
                        theme="borderless"
                        className={styles.chattopbar_right_input_clear}
                        onClick={(e) => {
                          e.stopPropagation();
                          setFindInputVisible(false);
                        }}
                      >
                        取消
                      </Button>
                    }
                  />
                </InputGroup>
              ) : (
                <FindIcon className={styles.chattopbar_right_find} onClick={handleFindClick} />
              )} */}
              {/* <div className={styles.chattopbar_right_help}>
              <Tooltip content={'帮助'} position="bottom">
                <IconIssueStroked />
              </Tooltip>
            </div> */}
              {!['/chat/office/meeting'].includes(pathname) ? (
                <PWAInstallButton
                  onInstalled={() => Toast.success('应用已成功安装！')}
                  onError={(error) => Toast.error(`安装失败: ${error.message}`)}
                />
              ) : (
                ''
              )}

              <Popover
                content={userProfile()}
                position="bottomLeft"
                trigger="click"
                className={styles.chattopbar_userinfo_popover}
              >
                <div className={styles.chattopbar_userinfo}>
                  <div className={styles.chattopbar_userinfo_avatar}>
                    <Avatar size="extra-small" style={{ margin: 4 }} alt="User" src={user?.avatar}>
                      {user?.username}
                    </Avatar>
                  </div>
                  <div className={styles.chattopbar_userinfo_name}>
                    {user?.name || user?.username}
                  </div>
                </div>
              </Popover>
            </div>
          </div>
        )}
        <div
          className={(() => {
            let s = [];
            s.push(styles.chatMain);
            if (['meeting', 'report_gen', 'write', 'ent_credit'].includes(pageMode)) {
              s.push(styles.chatMainWrapper);
            } else if (chatId && !['doc', 'ppt'].includes(pageMode)) {
              s.push(styles.chatMainHChatId);
            }
            return s.join(' ');
          })()}
        >
          <Outlet />
        </div>
      </div>
      <Modal
        title="编辑对话名称"
        visible={visible}
        onOk={updateTitle}
        onCancel={handleCancel}
        closeOnEsc={true}
      >
        <Input
          defaultValue={modalTitle}
          onChange={setModalTitle}
          maxLength={30}
          getValueLength={getValueLength}
        ></Input>
      </Modal>
    </>
  );
}
