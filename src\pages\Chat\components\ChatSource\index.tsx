import { getPreviewOfFile } from '@/services/chat';
import { fileOnlinePreview } from '@/utils';
import styles from './index.less';
const ChatSource = ({ quoteList }: { quoteList: any[] }) => {
  // const [quote, setQuote] = useState<any>();
  // const [content, setContent] = useState('');
  // useEffect(() => {
  //   try {
  //     const result = typeof quote?.hlQ === 'string' ? [quote?.hlQ] : quote?.hlQ;
  //     if (result) {
  //       const cont = result?.reduce((acc: string, item: string) => {
  //         return acc + item.replace(/<br?>/g, '\n');
  //       }, '');
  //       if (cont && typeof cont === 'string') {
  //         setContent(cont);
  //       } else {
  //         setContent('');
  //       }
  //     } else {
  //       setContent('');
  //     }
  //   } catch (error) {
  //     setContent('');
  //   }
  // }, [quote]);

  const components: { [key: string]: React.FC<{ children: React.ReactNode }> } = {};
  components['Highlight'] = ({ children }) => <span style={{ color: '#005BF8' }}>{children}</span>;

  const handleClick = async (item: any) => {
    const { data } = await getPreviewOfFile(item?.collectionId);
    const typedData = data as { value: string; type: string };
    if (typedData?.value) {
      fileOnlinePreview(typedData.value, '_black');
    } else {
      console.log('文件不存在');
    }
  };
  return (
    <>
      <div className={styles.chatSource}>
        <p className="mb-3">搜索来源：</p>
        <div className="flex flex-col flex-wrap gap-2 cursor-pointer">
          {quoteList.slice(0, 3).map((item) => (
            <p
              key={item.id}
              className="text-sm leading-22px text-[#005BF8]"
              onClick={() => handleClick(item)}
            >
              {item.sourceName}
            </p>
          ))}
        </div>
      </div>
      {/* 去掉弹窗展示形式 */}
      {/* <Modal
        title={
          <div className={styles.modalTitle} onClick={modalTitleHandleClick}>
            {quote?.sourceName}
          </div>
        }
        visible={modalVisible}
        size="medium"
        centered
        onCancel={() => setModalVisible(false)}
        footer={
          <Button theme="outline" type="tertiary" onClick={() => setModalVisible(false)}>
            知道了
          </Button>
        }
        bodyStyle={{ overflow: 'auto', maxHeight: 300 }}
      >
        <MarkdownRender
          style={{ whiteSpace: 'pre-wrap' }}
          raw={content}
          // components={components}
          format="md"
          onError={() => {
            setContent('');
          }}
        />
      </Modal> */}
    </>
  );
};

export default ChatSource;
